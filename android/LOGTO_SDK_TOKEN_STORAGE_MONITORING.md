# Logto SDK令牌存储监控

## 🎯 目标

添加详细的日志监控来查看Logto SDK的令牌存储过程，包括：
- 访问令牌的安全存储
- 刷新令牌的内部管理
- ID令牌和用户信息的缓存
- 本地存储与SDK存储的数据一致性

## 🔧 实现的监控功能

### 1. **LogtoManager增强**

#### **A. 登录成功后的令牌存储检查**
```kotlin
suspend fun signIn(activity: Activity): Unit = suspendCancellableCoroutine { continuation ->
    // ... 登录逻辑
    } else {
        Log.i(TAG, "✅ 登录成功")
        
        // 登录成功后，检查Logto SDK的令牌存储
        checkLogtoTokenStorage()
        
        continuation.resume(Unit)
    }
}
```

#### **B. checkLogtoTokenStorage方法**
```kotlin
private fun checkLogtoTokenStorage() {
    Log.i(TAG, "🔍 检查Logto SDK的令牌存储状态")
    
    // 1. 检查认证状态
    val isAuthenticated = logtoClient?.isAuthenticated ?: false
    Log.d(TAG, "📱 Logto SDK认证状态: $isAuthenticated")
    
    // 2. 检查访问令牌
    logtoClient?.getAccessToken { exception, accessToken ->
        if (exception != null) {
            Log.w(TAG, "⚠️ 获取访问令牌失败: ${exception.message}")
        } else {
            val token = accessToken?.token
            if (token != null) {
                Log.i(TAG, "🔑 Logto SDK访问令牌: ${token.take(20)}...")
                Log.d(TAG, "🔑 访问令牌长度: ${token.length}")
                Log.d(TAG, "🔑 访问令牌过期时间: ${accessToken.expiresAt}")
                
                // 检查令牌格式
                if (token.contains(".")) {
                    val parts = token.split(".")
                    Log.d(TAG, "🔑 JWT令牌结构: header.payload.signature (${parts.size}部分)")
                } else {
                    Log.w(TAG, "⚠️ 访问令牌不是标准JWT格式")
                }
            }
        }
    }
    
    // 3. 检查刷新令牌
    Log.d(TAG, "🔄 刷新令牌由Logto SDK内部管理，不直接暴露")
    
    // 4. 检查ID令牌和用户信息
    Log.d(TAG, "🆔 ID令牌声明由Logto SDK内部管理")
    Log.d(TAG, "🆔 用户信息需要通过异步方法获取")
}
```

### 2. **AuthManager增强**

#### **A. 应用启动时的存储检查**
```kotlin
scope.launch {
    Log.i(TAG, "🔍 开始检查初始认证状态")
    
    // 打印存储的认证数据用于调试
    userPreferences.printAuthData()
    
    // 检查Logto SDK的安全存储状态
    checkLogtoSecureStorage()
    
    // ... 其他逻辑
}
```

#### **B. 登录成功后的存储检查**
```kotlin
// 检查Logto SDK的安全存储状态
checkLogtoSecureStorage()
```

#### **C. checkLogtoSecureStorage方法**
```kotlin
private suspend fun checkLogtoSecureStorage() {
    Log.i(TAG, "🔐 检查Logto SDK的安全存储状态")
    
    try {
        // 1. 检查Logto SDK的认证状态
        val isLogtoAuthenticated = logtoManager.isAuthenticated
        Log.d(TAG, "📱 Logto SDK认证状态: $isLogtoAuthenticated")
        
        // 2. 检查Logto SDK是否有存储的令牌
        val logtoAccessToken = logtoManager.getAccessToken()
        if (logtoAccessToken != null) {
            Log.i(TAG, "🔑 Logto SDK安全存储中的访问令牌: ${logtoAccessToken.take(20)}...")
            Log.d(TAG, "🔑 令牌长度: ${logtoAccessToken.length}")
            
            // 分析令牌格式
            if (logtoAccessToken.contains(".")) {
                val parts = logtoAccessToken.split(".")
                Log.d(TAG, "🔑 JWT格式令牌，包含${parts.size}部分")
                
                // 尝试解析JWT header（仅用于调试）
                try {
                    val header = parts[0]
                    Log.d(TAG, "🔑 JWT Header (Base64): ${header.take(50)}...")
                } catch (e: Exception) {
                    Log.d(TAG, "🔑 无法解析JWT Header: ${e.message}")
                }
            } else {
                Log.w(TAG, "⚠️ 非标准JWT格式的令牌")
            }
        } else {
            Log.w(TAG, "⚠️ Logto SDK安全存储中没有访问令牌")
        }
        
        // 3. 检查用户信息
        val logtoUserInfo = logtoManager.getUserInfo()
        if (logtoUserInfo != null) {
            Log.i(TAG, "👤 Logto SDK安全存储中的用户信息:")
            logtoUserInfo.forEach { (key, value) ->
                Log.d(TAG, "  - $key: $value")
            }
        } else {
            Log.w(TAG, "⚠️ Logto SDK安全存储中没有用户信息")
        }
        
        // 4. 比较本地存储和Logto SDK存储的数据
        val localAccessToken = userPreferences.getAccessToken().first()
        if (localAccessToken != null && logtoAccessToken != null) {
            val isTokenSame = localAccessToken == logtoAccessToken
            Log.d(TAG, "🔄 本地存储与Logto SDK存储的令牌是否一致: $isTokenSame")
            if (!isTokenSame) {
                Log.w(TAG, "⚠️ 本地存储的令牌与Logto SDK存储的令牌不一致")
                Log.d(TAG, "  - 本地令牌: ${localAccessToken.take(20)}...")
                Log.d(TAG, "  - SDK令牌: ${logtoAccessToken.take(20)}...")
            }
        }
        
    } catch (e: Exception) {
        Log.e(TAG, "❌ 检查Logto SDK安全存储失败: ${e.message}", e)
    }
}
```

## 📊 监控的关键信息

### 1. **Logto SDK认证状态**
- `isAuthenticated`: SDK的认证状态
- 与本地存储状态的对比

### 2. **访问令牌分析**
- 令牌内容（前20个字符）
- 令牌长度
- 令牌过期时间
- JWT格式验证（header.payload.signature）
- JWT Header解析（Base64编码）

### 3. **刷新令牌管理**
- Logto SDK内部管理，不直接暴露
- 自动刷新机制

### 4. **用户信息存储**
- ID令牌声明
- 用户信息缓存
- 异步获取机制

### 5. **数据一致性检查**
- 本地存储 vs Logto SDK存储
- 令牌一致性验证
- 数据同步状态

## 🔍 预期日志输出

### **应用启动时**：
```
🔍 开始检查初始认证状态
=== 存储的认证数据 ===
ACCESS_TOKEN: X9u7og6ifJ25XgnYmG3_...
🔐 检查Logto SDK的安全存储状态
📱 Logto SDK认证状态: true
🔑 Logto SDK安全存储中的访问令牌: X9u7og6ifJ25XgnYmG3_...
🔑 令牌长度: 43
⚠️ 非标准JWT格式的令牌
👤 Logto SDK安全存储中的用户信息:
  - sub: e1l1m147ybvk
  - email: <EMAIL>
  - name: 
🔄 本地存储与Logto SDK存储的令牌是否一致: true
```

### **登录成功后**：
```
✅ 登录成功
🔍 检查Logto SDK的令牌存储状态
📱 Logto SDK认证状态: true
🔑 Logto SDK访问令牌: DsPcZQ2saiKjxylWQ9dG...
🔑 访问令牌长度: 200
🔑 访问令牌过期时间: 1752640739000
🔑 JWT令牌结构: header.payload.signature (3部分)
🔑 JWT Header (Base64): eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
🔄 刷新令牌由Logto SDK内部管理，不直接暴露
🆔 ID令牌声明由Logto SDK内部管理
🆔 用户信息需要通过异步方法获取

🔐 检查Logto SDK的安全存储状态
🔄 本地存储与Logto SDK存储的令牌是否一致: true
```

## 🚀 使用方法

1. **重新编译应用**：`./gradlew compileDebugKotlin`
2. **运行应用**并查看日志
3. **执行登录操作**
4. **观察Logto SDK存储过程**
5. **重启应用**查看存储持久化

## 🔧 调试要点

### **关注的日志标签**：
- `LogtoManager`: Logto SDK相关操作
- `AuthManager`: 认证管理和存储检查
- `UserPreferences`: 本地存储操作

### **关键指标**：
- 令牌长度和格式
- JWT结构完整性
- 本地存储与SDK存储的一致性
- 认证状态同步

### **常见问题排查**：
- 如果令牌长度<100，可能是SDK内部令牌
- 如果令牌不包含"."，不是标准JWT格式
- 如果本地存储与SDK存储不一致，需要同步

---

**总结**：通过这些监控功能，您可以详细了解Logto SDK的令牌存储机制，包括安全存储的使用、令牌格式、数据一致性等关键信息，有助于调试和优化认证流程。🎉
